"use client";

import type { PluginComponent } from "@payloadcms/richtext-lexical";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
	$getSelection,
	$isRangeSelection,
	COMMAND_PRIORITY_LOW,
	createCommand,
	type LexicalCommand,
} from "lexical";
import { $patchStyleText } from "@lexical/selection";
import { useEffect } from "react";
import { createClientFeature } from "@payloadcms/richtext-lexical/client";

// Define the command
export const TEXT_COLOR_COMMAND: LexicalCommand<string> =
	createCommand<string>();

// Color options
const COLORS = [
	{ label: "Black", value: "black" },
	{ label: "Red", value: "red" },
	{ label: "Green", value: "green" },
	{ label: "Blue", value: "blue" },
	{ label: "Orange", value: "orange" },
];

// Lexical plugin to register the command
const TextColorPlugin: PluginComponent = () => {
	const [editor] = useLexicalComposerContext();

	useEffect(() => {
		return editor.registerCommand<string>(
			TEXT_COLOR_COMMAND,
			(color) => {
				editor.update(() => {
					const selection = $getSelection();
					if ($isRangeSelection(selection)) {
						$patchStyleText(selection, { color });
					}
				});
				return true;
			},
			COMMAND_PRIORITY_LOW,
		);
	}, [editor]);

	return null;
};

// Toolbar dropdown to choose text color
const TextColorDropdown = () => {
	const [editor] = useLexicalComposerContext();

	return (
		<select
			defaultValue={COLORS[0].value}
			onChange={(e) => {
				const color = e.target.value;
				if (color) {
					editor.dispatchCommand(TEXT_COLOR_COMMAND, color);
				}
			}}
			className="text-sm border px-2 py-1 rounded"
		>
			<option value="">Text Color</option>
			{COLORS.map(({ label, value }) => (
				<option key={value} value={value}>
					{label}
				</option>
			))}
		</select>
	);
};

export const TextColorFeature = createClientFeature({
	plugins: [{ Component: TextColorPlugin, position: "normal" }],
	toolbarFixed: {
		groups: [
			{
				type: "buttons",
				key: "text-color-group",
				items: [
					{
						ChildComponent: TextColorDropdown,
						key: "text-color",
					},
				],
			},
		],
	},
});
